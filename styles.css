/* CSS Dragon - Advanced 3D Dragon with Realistic Features */

:root {
	--dragon-primary: #2c5530;
	--dragon-secondary: #1a3d1f;
	--dragon-accent: #4a7c59;
	--dragon-belly: #8fbc8f;
	--dragon-scales: #1e4d23;
	--dragon-eye: #ff6b35;
	--dragon-fire: #ff4500;
	--wing-membrane: rgba(139, 69, 19, 0.7);
	--claw-color: #2f2f2f;
	--horn-color: #8b4513;
	--sky-gradient: linear-gradient(135deg, #87ceeb 0%, #98d8e8 50%, #b0e0e6 100%);
	--mountain-color: #696969;
}

* {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
}

body {
	font-family: 'Arial', sans-serif;
	overflow: hidden;
	background: var(--sky-gradient);
	height: 100vh;
	perspective: 2000px;
}

.scene {
	width: 100vw;
	height: 100vh;
	position: relative;
	transform-style: preserve-3d;
	display: flex;
	align-items: center;
	justify-content: center;
}

/* Background Environment */
.background {
	position: absolute;
	width: 100%;
	height: 100%;
	z-index: -1;
}

.mountain {
	position: absolute;
	bottom: 0;
	background: var(--mountain-color);
	clip-path: polygon(0% 100%, 50% 20%, 100% 100%);
}

.mountain-1 {
	left: -10%;
	width: 40%;
	height: 60%;
	background: linear-gradient(135deg, #696969 0%, #2f4f4f 100%);
}

.mountain-2 {
	left: 20%;
	width: 50%;
	height: 70%;
	background: linear-gradient(135deg, #708090 0%, #2f4f4f 100%);
}

.mountain-3 {
	right: -10%;
	width: 45%;
	height: 55%;
	background: linear-gradient(135deg, #778899 0%, #2f4f4f 100%);
}

.clouds {
	position: absolute;
	width: 100%;
	height: 100%;
}

.cloud {
	position: absolute;
	background: rgba(255, 255, 255, 0.8);
	border-radius: 50px;
	animation: float 20s infinite ease-in-out;
}

.cloud::before,
.cloud::after {
	content: '';
	position: absolute;
	background: rgba(255, 255, 255, 0.8);
	border-radius: 50px;
}

.cloud-1 {
	top: 10%;
	left: 10%;
	width: 100px;
	height: 40px;
	animation-delay: 0s;
}

.cloud-1::before {
	top: -20px;
	left: 10px;
	width: 50px;
	height: 50px;
}

.cloud-1::after {
	top: -10px;
	right: 15px;
	width: 60px;
	height: 40px;
}

.cloud-2 {
	top: 20%;
	right: 15%;
	width: 80px;
	height: 35px;
	animation-delay: -7s;
}

.cloud-2::before {
	top: -15px;
	left: 15px;
	width: 40px;
	height: 40px;
}

.cloud-2::after {
	top: -8px;
	right: 10px;
	width: 50px;
	height: 35px;
}

.cloud-3 {
	top: 5%;
	left: 60%;
	width: 120px;
	height: 45px;
	animation-delay: -14s;
}

.cloud-3::before {
	top: -25px;
	left: 20px;
	width: 60px;
	height: 60px;
}

.cloud-3::after {
	top: -12px;
	right: 20px;
	width: 70px;
	height: 45px;
}

@keyframes float {
	0%,
	100% {
		transform: translateX(0) translateY(0);
	}
	25% {
		transform: translateX(20px) translateY(-10px);
	}
	50% {
		transform: translateX(-10px) translateY(-5px);
	}
	75% {
		transform: translateX(15px) translateY(-15px);
	}
}

/* Dragon Container */
.dragon {
	position: relative;
	transform-style: preserve-3d;
	animation: breathe 4s infinite ease-in-out;
	transform: scale(0.8) rotateY(-15deg) rotateX(5deg);
}

@keyframes breathe {
	0%,
	100% {
		transform: scale(0.8) rotateY(-15deg) rotateX(5deg) translateY(0);
	}
	50% {
		transform: scale(0.82) rotateY(-15deg) rotateX(5deg) translateY(-5px);
	}
}

/* Dragon Head */
.head {
	position: relative;
	width: 200px;
	height: 150px;
	transform: translateZ(100px) translateY(-50px) translateX(-50px);
	transform-style: preserve-3d;
}

.skull {
	position: relative;
	width: 100%;
	height: 100%;
	background: linear-gradient(
		135deg,
		var(--dragon-primary) 0%,
		var(--dragon-secondary) 50%,
		var(--dragon-accent) 100%
	);
	border-radius: 60px 60px 40px 40px;
	transform-style: preserve-3d;
	box-shadow: inset -10px -10px 20px rgba(0, 0, 0, 0.3), inset 10px 10px 20px rgba(255, 255, 255, 0.1),
		0 10px 30px rgba(0, 0, 0, 0.4);
}

.skull::before {
	content: '';
	position: absolute;
	top: 20px;
	left: 10px;
	right: 10px;
	height: 60px;
	background: repeating-linear-gradient(
		45deg,
		var(--dragon-scales) 0px,
		var(--dragon-scales) 3px,
		var(--dragon-primary) 3px,
		var(--dragon-primary) 6px
	);
	border-radius: 50px;
	opacity: 0.6;
}

.snout {
	position: absolute;
	bottom: -20px;
	left: 50%;
	transform: translateX(-50%);
	width: 80px;
	height: 60px;
	background: linear-gradient(135deg, var(--dragon-accent) 0%, var(--dragon-secondary) 100%);
	border-radius: 40px 40px 20px 20px;
	box-shadow: inset -5px -5px 10px rgba(0, 0, 0, 0.3), 0 5px 15px rgba(0, 0, 0, 0.3);
}

.nostril {
	position: absolute;
	top: 15px;
	width: 8px;
	height: 12px;
	background: #000;
	border-radius: 50%;
	box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.8);
}

.nostril-left {
	left: 20px;
}
.nostril-right {
	right: 20px;
}

.mouth {
	position: absolute;
	bottom: 5px;
	left: 50%;
	transform: translateX(-50%);
	width: 60px;
	height: 20px;
	background: #000;
	border-radius: 0 0 30px 30px;
	overflow: hidden;
}

.teeth {
	position: absolute;
	width: 100%;
	height: 8px;
	background: repeating-linear-gradient(90deg, #fff 0px, #fff 4px, transparent 4px, transparent 8px);
}

.teeth-upper {
	top: 0;
	clip-path: polygon(0 0, 100% 0, 90% 100%, 80% 0, 70% 100%, 60% 0, 50% 100%, 40% 0, 30% 100%, 20% 0, 10% 100%, 0 0);
}

.teeth-lower {
	bottom: 0;
	clip-path: polygon(0 100%, 10% 0, 20% 100%, 30% 0, 40% 100%, 50% 0, 60% 100%, 70% 0, 80% 100%, 90% 0, 100% 100%);
}

.tongue {
	position: absolute;
	bottom: 2px;
	left: 50%;
	transform: translateX(-50%);
	width: 40px;
	height: 6px;
	background: #8b0000;
	border-radius: 20px;
}

/* Dragon Eyes */
.eye {
	position: absolute;
	width: 40px;
	height: 30px;
	top: 40px;
	background: var(--dragon-eye);
	border-radius: 50%;
	box-shadow: inset -5px -5px 10px rgba(0, 0, 0, 0.3), inset 5px 5px 10px rgba(255, 255, 255, 0.2),
		0 0 20px var(--dragon-fire);
	animation: blink 6s infinite;
}

.eye-left {
	left: 30px;
}
.eye-right {
	right: 30px;
}

.pupil {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 15px;
	height: 20px;
	background: #000;
	border-radius: 50%;
	box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.8);
}

.iris {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 25px;
	height: 25px;
	background: radial-gradient(circle, var(--dragon-fire) 0%, #8b0000 70%, #000 100%);
	border-radius: 50%;
	z-index: -1;
}

.eyelid {
	position: absolute;
	top: -5px;
	left: -5px;
	right: -5px;
	bottom: -5px;
	background: var(--dragon-primary);
	border-radius: 50%;
	z-index: 1;
	opacity: 0;
	transition: opacity 0.2s;
}

@keyframes blink {
	0%,
	90%,
	100% {
		opacity: 1;
	}
	95% {
		opacity: 0;
	}
}

/* Dragon Horns */
.horn {
	position: absolute;
	background: linear-gradient(135deg, var(--horn-color) 0%, #654321 100%);
	border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
	box-shadow: inset -2px -2px 5px rgba(0, 0, 0, 0.3), 0 5px 10px rgba(0, 0, 0, 0.3);
}

.horn-left {
	top: 10px;
	left: 20px;
	width: 15px;
	height: 40px;
	transform: rotate(-20deg);
}

.horn-right {
	top: 10px;
	right: 20px;
	width: 15px;
	height: 40px;
	transform: rotate(20deg);
}

.horn-center {
	top: 5px;
	left: 50%;
	transform: translateX(-50%);
	width: 12px;
	height: 35px;
}

/* Dragon Jaw */
.jaw {
	position: absolute;
	bottom: -40px;
	left: 50%;
	transform: translateX(-50%);
	width: 100px;
	height: 40px;
	background: linear-gradient(135deg, var(--dragon-secondary) 0%, var(--dragon-primary) 100%);
	border-radius: 0 0 50px 50px;
	box-shadow: inset -5px -5px 10px rgba(0, 0, 0, 0.3), 0 5px 15px rgba(0, 0, 0, 0.3);
}

.jaw-teeth {
	position: absolute;
	top: 0;
	width: 100%;
	height: 10px;
	background: repeating-linear-gradient(90deg, #fff 0px, #fff 5px, transparent 5px, transparent 10px);
	clip-path: polygon(0 0, 100% 0, 95% 100%, 85% 0, 75% 100%, 65% 0, 55% 100%, 45% 0, 35% 100%, 25% 0, 15% 100%, 5% 0);
}

/* Dragon Neck */
.neck {
	position: relative;
	transform: translateZ(50px) translateY(50px) translateX(50px);
	transform-style: preserve-3d;
}

.neck-segment {
	position: absolute;
	width: 60px;
	height: 80px;
	background: linear-gradient(
		135deg,
		var(--dragon-primary) 0%,
		var(--dragon-secondary) 50%,
		var(--dragon-accent) 100%
	);
	border-radius: 30px;
	box-shadow: inset -5px -5px 15px rgba(0, 0, 0, 0.3), inset 5px 5px 15px rgba(255, 255, 255, 0.1),
		0 5px 20px rgba(0, 0, 0, 0.3);
}

.neck-segment::before {
	content: '';
	position: absolute;
	top: 10px;
	left: 5px;
	right: 5px;
	height: 60px;
	background: repeating-linear-gradient(
		90deg,
		var(--dragon-scales) 0px,
		var(--dragon-scales) 2px,
		var(--dragon-primary) 2px,
		var(--dragon-primary) 4px
	);
	border-radius: 25px;
	opacity: 0.4;
}

.segment-1 {
	transform: translateY(0) rotateZ(-10deg);
}

.segment-2 {
	transform: translateY(70px) rotateZ(-5deg);
}

.segment-3 {
	transform: translateY(140px) rotateZ(0deg);
}

.segment-4 {
	transform: translateY(210px) rotateZ(5deg);
}

.neck-spines {
	position: relative;
	transform-style: preserve-3d;
}

.neck-spines .spine {
	position: absolute;
	width: 8px;
	height: 25px;
	background: linear-gradient(135deg, var(--horn-color) 0%, #654321 100%);
	border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
	left: 50%;
	transform: translateX(-50%);
	box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
}

.neck-spines .spine-1 {
	top: -10px;
	transform: translateX(-50%) translateY(0) rotateZ(-10deg);
}

.neck-spines .spine-2 {
	top: 60px;
	transform: translateX(-50%) translateY(70px) rotateZ(-5deg);
}

.neck-spines .spine-3 {
	top: 130px;
	transform: translateX(-50%) translateY(140px) rotateZ(0deg);
}

.neck-spines .spine-4 {
	top: 200px;
	transform: translateX(-50%) translateY(210px) rotateZ(5deg);
}

/* Dragon Body */
.body {
	position: relative;
	transform: translateZ(0) translateY(200px) translateX(100px);
	transform-style: preserve-3d;
}

.torso {
	position: relative;
	width: 300px;
	height: 200px;
	background: linear-gradient(
		135deg,
		var(--dragon-primary) 0%,
		var(--dragon-secondary) 50%,
		var(--dragon-accent) 100%
	);
	border-radius: 150px 150px 100px 100px;
	transform-style: preserve-3d;
	box-shadow: inset -15px -15px 30px rgba(0, 0, 0, 0.3), inset 15px 15px 30px rgba(255, 255, 255, 0.1),
		0 15px 40px rgba(0, 0, 0, 0.4);
}

.torso::before {
	content: '';
	position: absolute;
	top: 20px;
	left: 20px;
	right: 20px;
	height: 160px;
	background: repeating-linear-gradient(
		45deg,
		var(--dragon-scales) 0px,
		var(--dragon-scales) 4px,
		var(--dragon-primary) 4px,
		var(--dragon-primary) 8px
	);
	border-radius: 130px 130px 80px 80px;
	opacity: 0.5;
}

.chest {
	position: absolute;
	top: 40px;
	left: 50%;
	transform: translateX(-50%);
	width: 200px;
	height: 80px;
	background: linear-gradient(135deg, var(--dragon-accent) 0%, var(--dragon-belly) 100%);
	border-radius: 100px;
	box-shadow: inset -10px -10px 20px rgba(0, 0, 0, 0.2);
}

.belly {
	position: absolute;
	bottom: 20px;
	left: 50%;
	transform: translateX(-50%);
	width: 250px;
	height: 60px;
	background: linear-gradient(135deg, var(--dragon-belly) 0%, var(--dragon-accent) 100%);
	border-radius: 125px;
	box-shadow: inset -8px -8px 15px rgba(0, 0, 0, 0.2);
}

.back-spines {
	position: absolute;
	top: -20px;
	left: 50%;
	transform: translateX(-50%);
	width: 100%;
	transform-style: preserve-3d;
}

.back-spine {
	position: absolute;
	width: 12px;
	height: 40px;
	background: linear-gradient(135deg, var(--horn-color) 0%, #654321 100%);
	border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
	box-shadow: 0 5px 12px rgba(0, 0, 0, 0.3);
}

.back-spine.spine-1 {
	left: 20%;
	transform: rotateZ(-15deg);
}

.back-spine.spine-2 {
	left: 35%;
	transform: rotateZ(-5deg);
}

.back-spine.spine-3 {
	left: 50%;
	transform: translateX(-50%);
}

.back-spine.spine-4 {
	left: 65%;
	transform: rotateZ(5deg);
}

.back-spine.spine-5 {
	left: 80%;
	transform: rotateZ(15deg);
}

/* Dragon Wings */
.wings {
	position: relative;
	transform: translateZ(-20px) translateY(150px) translateX(150px);
	transform-style: preserve-3d;
}

.wing {
	position: absolute;
	transform-style: preserve-3d;
	animation: wingFlap 6s infinite ease-in-out;
}

.wing-left {
	left: -200px;
	transform-origin: right center;
}

.wing-right {
	right: -200px;
	transform-origin: left center;
	transform: scaleX(-1);
}

@keyframes wingFlap {
	0%,
	100% {
		transform: rotateY(0deg) rotateZ(0deg);
	}
	25% {
		transform: rotateY(-10deg) rotateZ(-5deg);
	}
	50% {
		transform: rotateY(-20deg) rotateZ(-10deg);
	}
	75% {
		transform: rotateY(-10deg) rotateZ(-5deg);
	}
}

.wing-bone {
	position: absolute;
	background: linear-gradient(135deg, var(--dragon-secondary) 0%, var(--dragon-primary) 100%);
	border-radius: 8px;
	box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
}

.wing-bone.bone-1 {
	width: 8px;
	height: 120px;
	top: 0;
	left: 0;
	transform-origin: bottom;
	transform: rotateZ(10deg);
}

.wing-bone.bone-2 {
	width: 6px;
	height: 100px;
	top: 20px;
	left: 40px;
	transform-origin: bottom;
	transform: rotateZ(25deg);
}

.wing-bone.bone-3 {
	width: 5px;
	height: 80px;
	top: 40px;
	left: 80px;
	transform-origin: bottom;
	transform: rotateZ(40deg);
}

.wing-bone.bone-4 {
	width: 4px;
	height: 60px;
	top: 60px;
	left: 120px;
	transform-origin: bottom;
	transform: rotateZ(55deg);
}

.wing-membrane {
	position: absolute;
	background: var(--wing-membrane);
	border-radius: 20px 5px;
	box-shadow: inset -5px -5px 15px rgba(0, 0, 0, 0.3);
}

.wing-membrane.membrane-1 {
	width: 60px;
	height: 100px;
	top: 10px;
	left: 8px;
	transform: rotateZ(15deg);
	clip-path: polygon(0 0, 80% 0, 100% 100%, 0 90%);
}

.wing-membrane.membrane-2 {
	width: 70px;
	height: 90px;
	top: 25px;
	left: 46px;
	transform: rotateZ(30deg);
	clip-path: polygon(0 0, 85% 0, 100% 100%, 0 85%);
}

.wing-membrane.membrane-3 {
	width: 60px;
	height: 75px;
	top: 45px;
	left: 85px;
	transform: rotateZ(45deg);
	clip-path: polygon(0 0, 90% 0, 100% 100%, 0 80%);
}

.wing-membrane.membrane-4 {
	width: 50px;
	height: 55px;
	top: 65px;
	left: 124px;
	transform: rotateZ(60deg);
	clip-path: polygon(0 0, 95% 0, 100% 100%, 0 75%);
}

.wing-claw {
	position: absolute;
	top: -10px;
	left: 15px;
	width: 15px;
	height: 25px;
	background: linear-gradient(135deg, var(--claw-color) 0%, #1a1a1a 100%);
	border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
	transform: rotateZ(45deg);
	box-shadow: 0 3px 8px rgba(0, 0, 0, 0.4);
}

/* Dragon Legs */
.legs {
	position: relative;
	transform: translateZ(10px) translateY(300px) translateX(150px);
	transform-style: preserve-3d;
}

.leg {
	position: absolute;
	transform-style: preserve-3d;
}

.leg-front-left {
	left: -50px;
	top: -50px;
	transform: rotateY(-20deg);
}

.leg-front-right {
	right: -50px;
	top: -50px;
	transform: rotateY(20deg);
}

.leg-back-left {
	left: 50px;
	top: 50px;
	transform: rotateY(-30deg);
}

.leg-back-right {
	right: 50px;
	top: 50px;
	transform: rotateY(30deg);
}

.thigh {
	position: relative;
	width: 40px;
	height: 80px;
	background: linear-gradient(135deg, var(--dragon-primary) 0%, var(--dragon-secondary) 100%);
	border-radius: 20px;
	box-shadow: inset -5px -5px 15px rgba(0, 0, 0, 0.3), inset 5px 5px 15px rgba(255, 255, 255, 0.1),
		0 5px 20px rgba(0, 0, 0, 0.3);
	transform-origin: top center;
}

.thigh::before {
	content: '';
	position: absolute;
	top: 10px;
	left: 5px;
	right: 5px;
	height: 60px;
	background: repeating-linear-gradient(
		45deg,
		var(--dragon-scales) 0px,
		var(--dragon-scales) 2px,
		var(--dragon-primary) 2px,
		var(--dragon-primary) 4px
	);
	border-radius: 15px;
	opacity: 0.4;
}

.shin {
	position: absolute;
	bottom: -60px;
	left: 50%;
	transform: translateX(-50%);
	width: 30px;
	height: 70px;
	background: linear-gradient(135deg, var(--dragon-secondary) 0%, var(--dragon-primary) 100%);
	border-radius: 15px;
	box-shadow: inset -3px -3px 10px rgba(0, 0, 0, 0.3), 0 3px 15px rgba(0, 0, 0, 0.3);
	transform-origin: top center;
}

.shin::before {
	content: '';
	position: absolute;
	top: 5px;
	left: 3px;
	right: 3px;
	height: 60px;
	background: repeating-linear-gradient(
		90deg,
		var(--dragon-scales) 0px,
		var(--dragon-scales) 1px,
		var(--dragon-secondary) 1px,
		var(--dragon-secondary) 2px
	);
	border-radius: 12px;
	opacity: 0.3;
}

.foot {
	position: absolute;
	bottom: -40px;
	left: 50%;
	transform: translateX(-50%);
	width: 50px;
	height: 30px;
	background: linear-gradient(135deg, var(--dragon-secondary) 0%, var(--claw-color) 100%);
	border-radius: 25px 25px 10px 10px;
	box-shadow: inset -3px -3px 8px rgba(0, 0, 0, 0.3), 0 3px 12px rgba(0, 0, 0, 0.4);
	transform-style: preserve-3d;
}

.claw {
	position: absolute;
	bottom: -15px;
	width: 8px;
	height: 20px;
	background: linear-gradient(135deg, var(--claw-color) 0%, #000 100%);
	border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
	box-shadow: 0 2px 6px rgba(0, 0, 0, 0.5);
}

.claw.claw-1 {
	left: 5px;
	transform: rotateZ(-15deg);
}

.claw.claw-2 {
	left: 15px;
	transform: rotateZ(-5deg);
}

.claw.claw-3 {
	right: 15px;
	transform: rotateZ(5deg);
}

.claw.claw-4 {
	right: 5px;
	transform: rotateZ(15deg);
}

/* Dragon Tail */
.tail {
	position: relative;
	transform: translateZ(-10px) translateY(350px) translateX(400px);
	transform-style: preserve-3d;
}

.tail-segment {
	position: absolute;
	background: linear-gradient(135deg, var(--dragon-primary) 0%, var(--dragon-secondary) 100%);
	border-radius: 50%;
	box-shadow: inset -5px -5px 15px rgba(0, 0, 0, 0.3), inset 5px 5px 15px rgba(255, 255, 255, 0.1),
		0 5px 20px rgba(0, 0, 0, 0.3);
}

.tail-segment::before {
	content: '';
	position: absolute;
	top: 20%;
	left: 20%;
	right: 20%;
	bottom: 20%;
	background: repeating-linear-gradient(
		45deg,
		var(--dragon-scales) 0px,
		var(--dragon-scales) 2px,
		var(--dragon-primary) 2px,
		var(--dragon-primary) 4px
	);
	border-radius: 50%;
	opacity: 0.4;
}

.tail-segment.segment-1 {
	width: 80px;
	height: 60px;
	transform: translateX(0) rotateZ(10deg);
}

.tail-segment.segment-2 {
	width: 70px;
	height: 50px;
	transform: translateX(70px) translateY(20px) rotateZ(20deg);
}

.tail-segment.segment-3 {
	width: 60px;
	height: 40px;
	transform: translateX(130px) translateY(50px) rotateZ(30deg);
}

.tail-segment.segment-4 {
	width: 50px;
	height: 35px;
	transform: translateX(180px) translateY(85px) rotateZ(40deg);
}

.tail-segment.segment-5 {
	width: 40px;
	height: 30px;
	transform: translateX(220px) translateY(125px) rotateZ(50deg);
}

.tail-segment.segment-6 {
	width: 30px;
	height: 25px;
	transform: translateX(250px) translateY(165px) rotateZ(60deg);
}

.tail-spikes {
	position: relative;
	transform-style: preserve-3d;
}

.tail-spike {
	position: absolute;
	width: 10px;
	height: 20px;
	background: linear-gradient(135deg, var(--horn-color) 0%, #654321 100%);
	border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
	box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
}

.tail-spike.spike-1 {
	top: -10px;
	left: 40px;
	transform: rotateZ(10deg);
}

.tail-spike.spike-2 {
	top: 10px;
	left: 105px;
	transform: rotateZ(20deg);
}

.tail-spike.spike-3 {
	top: 40px;
	left: 155px;
	transform: rotateZ(30deg);
}

.tail-spike.spike-4 {
	top: 75px;
	left: 195px;
	transform: rotateZ(40deg);
}

.tail-tip {
	position: absolute;
	top: 180px;
	left: 270px;
	transform: rotateZ(70deg);
	transform-style: preserve-3d;
}

.tail-blade {
	width: 25px;
	height: 40px;
	background: linear-gradient(135deg, var(--claw-color) 0%, #000 100%);
	border-radius: 50% 50% 50% 50% / 80% 80% 20% 20%;
	box-shadow: inset -2px -2px 5px rgba(0, 0, 0, 0.5), 0 5px 15px rgba(0, 0, 0, 0.5);
}

/* Responsive Design */
@media (max-width: 1200px) {
	.dragon {
		transform: scale(0.6) rotateY(-15deg) rotateX(5deg);
	}
}

@media (max-width: 768px) {
	.dragon {
		transform: scale(0.4) rotateY(-15deg) rotateX(5deg);
	}

	.mountain {
		height: 40% !important;
	}
}

@media (max-width: 480px) {
	.dragon {
		transform: scale(0.3) rotateY(-15deg) rotateX(5deg);
	}
}
